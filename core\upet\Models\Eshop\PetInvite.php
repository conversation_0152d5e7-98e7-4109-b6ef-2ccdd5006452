<?php

namespace Upet\Models\Eshop;

use Upet\Models\Model;

class PetInvite extends Model
{
    protected $pk = 'id';
    protected $table = 'eshop.pet_invite';


    /**
     * 根据 invitee_open_id 查询未注册的邀请记录
     * @param string $openId
     * @return Model|null
     */
    public static function getUnregisteredByOpenId($openId)
    {
        return self::where('invitee_open_id', $openId)
            ->where('invitee_register_status', 0)
            ->find();
    }

    /**
     * 根据ID更新被邀请人信息
     * @param int $id
     * @param array $updateData
     * @return bool
     */
    public static function updateInviteeInfoById($id, $updateData)
    {
        return self::where('id', $id)->update($updateData);
    }

    /**
     * 根据 invitee_id 批量更新被邀请人信息
     * @param string $openId
     * @param array $updateData
     * @return bool
     */
    public static function updateInviteeInfo($openId, $updateData)
    {
        $res = self::where('invitee_open_id', $openId)
            ->where('invitee_register_status', 0)
            ->update($updateData);
        return $res;
    }
}
