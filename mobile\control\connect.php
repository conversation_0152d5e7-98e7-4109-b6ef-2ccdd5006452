<?php

/**
 * 第三方账号登录
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use think\Db;
use Upet\Models\Datacenter\MemberInfo;
use Upet\Modules\Member\Actions\VerifyJwtSignAction;

defined('InShopNC') or exit('Access Invalid!');

class connectControl extends mobileHomeControl
{
    const KEY = 'URJoSEz2Fb8NwzNNGfplbYlolW7wph19UiqdLNkhlq4tdmbV98R9IwB3CyhPSrik';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 登录开关状态
     */
    public function get_stateOp()
    {
        $logic_connect_api = Logic('connect_api');
        $state_array = $logic_connect_api->getStateInfo();

        $key = $_GET['t'];
        if (trim($key) != '' && array_key_exists($key, $state_array)) {
            output_data($state_array[$key]);
        } else {
            output_data($state_array);
        }
    }

    /**
     * WAP页面微信登录回调
     */
    public function indexOp()
    {
        $logic_connect_api = Logic('connect_api');
        if (!empty($_GET['code'])) {
            $code = $_GET['code'];
            $client = 'wap';
            $user_info = $logic_connect_api->getWxUserInfo($code, 'wap');
            if (!empty($user_info['unionid'])) {
                $unionid = $user_info['unionid'];
                $model_member = Model('member');
                $member = $model_member->getMemberInfo(array('weixin_unionid' => $unionid));
                $state_data = array();
                $token = 0;
                if (!empty($member)) { //会员信息存在时自动登录
                    $this->check_member($member, $client);
                    $token = $logic_connect_api->getUserToken($member, $client);
                } else { //自动注册会员并登录
                    $info_data = $logic_connect_api->wxRegister($user_info, $client);
                    $token = $info_data['token'];
                    $member = $info_data['member'];
                    $state_data['password'] = $member['member_passwd'];
                }
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member['member_id'];
                    $_url = WAP_SITE_URL . '/tmpl/member/member.html?username=' . $state_data['username'] . '&key=' . $state_data['key'];
                    $url = base64_decode($_GET['state']);
                    if ($url) $_url = $_url . '&ref_url=' . urlencode($url);
                    redirect($_url);
                } else {
                    output_error('会员登录失败');
                }
            } else {
                output_error('微信登录失败');
            }
        } else {
            $_url = $logic_connect_api->getWxOAuth2Url();
            @header("location: " . $_url);
        }
    }

    /**
     * QQ互联获取应用唯一标识
     */
    public function get_qq_appidOp()
    {
        output_data(C('app_qq_akey'));
    }

    /**
     * 请求QQ互联授权
     */
    public function get_qq_oauth2Op()
    {
        $logic_connect_api = Logic('connect_api');
        $qq_url = $logic_connect_api->getQqOAuth2Url('api');
        @header("location: " . $qq_url);
    }

    /**
     * QQ互联获取回调信息
     */
    public function get_qq_infoOp()
    {
        $code = $_GET['code'];
        $token = $_GET['token'];
        $client = $_GET['client'];
        $logic_connect_api = Logic('connect_api');
        $user_info = $logic_connect_api->getQqUserInfo($code, $client, $token);
        if (!empty($user_info['openid'])) {
            $qqopenid = $user_info['openid'];
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_qqopenid' => $qqopenid));
            $state_data = array();
            $token = 0;
            if (!empty($member)) { //会员信息存在时自动登录
                $this->check_member($member, $client);
                $token = $logic_connect_api->getUserToken($member, $client);
            } else { //自动注册会员并登录
                $info_data = $logic_connect_api->qqRegister($user_info, $client);
                $token = $info_data['token'];
                $member = $info_data['member'];
                $state_data['password'] = $member['member_passwd'];
            }
            if ($token) {
                $state_data['key'] = $token;
                $state_data['username'] = $member['member_name'];
                $state_data['userid'] = $member['member_id'];
                if ($client == 'wap') {
                    $_url = WAP_SITE_URL . '/tmpl/member/member.html?username=' . $state_data['username'] . '&key=' . $state_data['key'];
                    $url = base64_decode($_GET['state']);
                    if ($url) $_url = $_url . '&ref_url=' . $url;
                    redirect($_url);
                }
                output_data($state_data);
            } else {
                output_error('会员登录失败');
            }
        } else {
            output_error('QQ互联登录失败');
        }
    }

    /**
     * 新浪微博获取应用唯一标识
     */
    public function get_sina_appidOp()
    {
        output_data(C('app_sina_akey'));
    }

    /**
     * 请求新浪微博授权
     */
    public function get_sina_oauth2Op()
    {
        $logic_connect_api = Logic('connect_api');
        $sina_url = $logic_connect_api->getSinaOAuth2Url('api');
        @header("location: " . $sina_url);
    }

    /**
     * 新浪微博获取回调信息
     */
    public function get_sina_infoOp()
    {
        $code = $_GET['code'];
        $client = $_GET['client'];
        $sina_token['access_token'] = $_GET['accessToken'];
        $sina_token['uid'] = $_GET['userID'];
        $logic_connect_api = Logic('connect_api');
        $user_info = $logic_connect_api->getSinaUserInfo($code, $client, $sina_token);
        if (!empty($user_info['id'])) {
            $sinaopenid = $user_info['id'];
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_sinaopenid' => $sinaopenid));
            $state_data = array();
            $token = 0;
            if (!empty($member)) { //会员信息存在时自动登录
                $this->check_member($member, $client);
                $token = $logic_connect_api->getUserToken($member, $client);
            } else { //自动注册会员并登录
                $info_data = $logic_connect_api->sinaRegister($user_info, $client);
                $token = $info_data['token'];
                $member = $info_data['member'];
                $state_data['password'] = $member['member_passwd'];
            }
            if ($token) {
                $state_data['key'] = $token;
                $state_data['username'] = $member['member_name'];
                $state_data['userid'] = $member['member_id'];
                if ($client == 'wap') {
                    $_url = WAP_SITE_URL . '/tmpl/member/member.html?username=' . $state_data['username'] . '&key=' . $state_data['key'];
                    $url = base64_decode($_GET['state']);
                    if ($url) $_url = $_url . '&ref_url=' . $url;
                    redirect($_url);
                }
                output_data($state_data);
            } else {
                output_error('会员登录失败');
            }
        } else {
            output_error('新浪微博登录失败');
        }
    }

    /**
     * 微信获取应用唯一标识
     */
    public function get_wx_appidOp()
    {
        output_data(C('app_weixin_appid'));
    }

    /**
     * 微信获取回调信息
     */
    public function get_wx_infoOp()
    {
        $code = $_GET['code'];
        $access_token = $_GET['access_token'];
        $openid = $_GET['openid'];
        $client = $_GET['client'];
        $logic_connect_api = Logic('connect_api');
        if (!empty($code)) {
            $user_info = $logic_connect_api->getWxUserInfo($code, 'api');
        } else {
            $user_info = $logic_connect_api->getWxUserInfoUmeng($access_token, $openid);
        }
        if (!empty($user_info['unionid'])) {
            $unionid = $user_info['unionid'];
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('weixin_unionid' => $unionid));
            $state_data = array();
            $token = 0;
            if (!empty($member)) { //会员信息存在时自动登录
                $this->check_member($member, $client);
                $token = $logic_connect_api->getUserToken($member, $client);
            } else { //自动注册会员并登录
                $info_data = $logic_connect_api->wxRegister($user_info, $client);
                $token = $info_data['token'];
                $member = $info_data['member'];
                $state_data['password'] = $member['member_passwd'];
            }
            if ($token) {
                $state_data['key'] = $token;
                $state_data['username'] = $member['member_name'];
                $state_data['userid'] = $member['member_id'];
                output_data($state_data);
            } else {
                output_error('会员登录失败');
            }
        } else {
            output_error('微信登录失败');
        }
    }

    /**
     * 获取手机短信动态码
     */
    public function get_sms_captchaOp()
    {
        $sec_key = $_GET['sec_key'];
        $sec_val = $_GET['sec_val'];

        $phone = $_GET['phone'];
        $log_type = $_GET['type']; //短信类型:1为注册,2为登录,3为找回密码
        $state_data = array(
            'state' => false,
            'msg' => '验证码或手机号码不正确'
        );

        $result = Model('apiseccode')->checkApiSeccode($sec_key, $sec_val);
        if ($result && strlen($phone) == 11) {
            $logic_connect_api = Logic('connect_api');
            $state_data = $logic_connect_api->sendCaptcha($phone, $log_type);
        }
        $this->connect_output_data($state_data);
    }

    /**
     * 验证手机动态码
     */
    public function check_sms_captchaOp()
    {
        $phone = $_GET['phone'];
        $captcha = $_GET['captcha'];
        $log_type = $_GET['type'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->checkSmsCaptcha($phone, $captcha, $log_type);
        $this->connect_output_data($state_data, 1);
    }

    /**
     * Notes:获取阿里云手机号验证码
     * User: rocky
     * DateTime: 2023/4/21 11:41
     */
    public function getSmsCodeOp()
    {
        $phone = $_GET['phone'];
        basicFormValidate($phone);
        if ($_GET['type'] > 0) {
            $member_info = $this->getMemberAndGradeInfo();
            if ($member_info['member_mobile'] !== $phone) {
                output_error('请输入当前绑定的手机号');
            }
            if ($_GET['type'] == 1) {
                output_data("验证通过，请进行下一步");
            }
        }
        $res = sendSms($phone);
        if ($res['code'] == 200) {
            output_data($res['msg']);
        }
        output_error($res['msg']);
    }

    /**
     * Notes:手机号验证码校验
     * User: rocky
     * DateTime: 2023/4/21 14:21
     */
    public function checkSmsCodeOp()
    {
        $res = checkSmsCode();
        output_data($res['msg']);
    }

    /**
     * 绑定手机注册
     */
    public function sms_register_bindOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $client = $_POST['client'];
        $logic_connect_api = Logic('connect_api');
        $member_info = $this->getMemberAndGradeInfo();

        $state_data = $logic_connect_api->smsRegisterBind($phone, $captcha, $client, $member_info);
        $this->connect_output_data($state_data);
    }

    /**
     * 手机注册
     */
    public function sms_registerOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsRegister($phone, $captcha, $password, $client);
        $this->connect_output_data($state_data);
    }

    /**
     * 手机找回密码
     */
    public function find_passwordOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsPassword($phone, $captcha, $password, $client);
        $this->connect_output_data($state_data);
    }

    /**
     * 格式化输出数据
     */
    public function connect_output_data($state_data, $type = 0)
    {
        if ($state_data['state']) {
            unset($state_data['state']);
            unset($state_data['msg']);
            if ($type == 1) {
                $state_data = 1;
            }
            output_data($state_data);
        } else {
            output_error($state_data['msg']);
        }
    }

    /**
     * 检查会员账号
     */
    public function check_member($member, $client)
    {
        if (!$member['member_state']) {
            if ($client == 'wap') {
                header('Content-type: text/html; charset=utf-8');
                echo "<script>";
                echo "alert('账号被停用');";
                echo "location.href='" . WAP_SITE_URL . "'";
                echo "</script>";
                exit;
            }
            output_error('账号被停用');
        }
    }

    /**
     * 小程序登录 注册(阿闻宠物-北京团队用)
     */
    public function sms_mobileOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $openid = $_POST['oid'];
        $type = $_POST['type'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsMobile($phone, $captcha, $password, $client, $openid, $type);
        $this->connect_output_data($state_data);
    }

    /**
     * 小程序登录 注册(阿闻宠物-北京团队用)
     */
    public function sms_mobile_loginOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $type = 1;
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->checkSmsCaptcha($phone, $captcha, $type); //再次进行动态码验证

        if ($state_data['state']) {
            $member_info = MemberInfo::where('mobile', '=', $phone)->find();
            $state_data['msg'] = '登录成功';
            if (!$member_info) {
                define('SCRIPT_ROOT', BASE_DATA_PATH . '/api/ERP');
                require_once SCRIPT_ROOT . '/base/' . 'member.php';
                $member_logic = Logic("erp_member");
                $member_id = $member_logic->memberadd($phone, true);
                if (!$member_id) {
                    $state_data['state'] = false;
                    $state_data['msg'] = '登录失败';
                }
            }
        }
        output_data($state_data);
    }

    /**
     * 小程序登录 注册(阿闻智慧宠物医院-自用)
     */
    public function sms_mobileshopOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $openid = $_POST['oid'];
        $type = $_POST['type'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsMobileShop($phone, $captcha, $password, $client, $openid, $type);
        $this->connect_output_data($state_data);
    }

    /**
     * 小程序登录 注册(阿闻商城-自用)
     */
    public function sms_mobilemallOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $openid = $_POST['oid'];
        $type = $_POST['type'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsMobileMall($phone, $captcha, $password, $client, $openid, $type);
        $this->connect_output_data($state_data);
    }

    /**
     * 小程序登录 注册(阿闻爱省钱-自用)
     */
    public function sms_mobileshopasqOp()
    {
        $phone = $_POST['phone'];
        $captcha = $_POST['captcha'];
        $password = $_POST['password'];
        $client = $_POST['client'];
        $openid = $_POST['oid'];
        $type = $_POST['type'];
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->smsMobileAsq($phone, $captcha, $password, $client, $openid, $type);
        $this->connect_output_data($state_data);
    }

    /**
     * 小程序绑定手机（阿闻宠物）
     */
    public function bind_mobileOp()
    {
        $model_mb_payment = Model('mb_payment');
        $condition = array();
        $condition['payment_code'] = "wxpay_jsapi";
        $mb_payment_info = $model_mb_payment->getMbPaymentOpenInfo($condition);
        $payment_config = $mb_payment_info['payment_config'];
        $data = json_decode(file_get_contents('php://input'), true);
        $openid = $data['info']['openid'];
        $iv = $data['info']['iv'];
        $result = $this->decryptData($payment_config['miniappId'], $data['info']['session_key'], $data['info']['encryptedData'], $iv, $data);
        $arrData = json_decode($result, true);
        $phone = $arrData['phoneNumber'];
        if ($phone) {
            $logic_connect_api = Logic('connect_api');
            $state_data = $logic_connect_api->smsRegisterBindMini($phone, $openid);
            $this->connect_output_data($state_data);
        } else {
            output_error("获取信息错误");
        }
    }

    /**
     * 小程序绑定手机（阿闻智慧宠物医院）
     */
    public function bind_mobileshopOp()
    {
        $model_mb_payment = Model('mb_payment');
        $condition = array();
        $condition['payment_code'] = "wxpay_jsapi";
        $mb_payment_info = $model_mb_payment->getMbPaymentOpenInfo($condition);
        $payment_config = $mb_payment_info['payment_config'];
        $data = json_decode(file_get_contents('php://input'), true);
        $openid = $data['info']['openid'];
        $iv = $data['info']['iv'];
        $result = $this->decryptData($payment_config['miniappId2'], $data['info']['session_key'], $data['info']['encryptedData'], $iv, $data);
        $arrData = json_decode($result, true);
        $phone = $arrData['phoneNumber'];
        if ($phone) {
            $logic_connect_api = Logic('connect_api');

            $state_data = $logic_connect_api->smsRegisterBindMinishop($phone, $openid);
            $this->connect_output_data($state_data);
        } else {
            $err_data = [];
            $err_data['data'] = $data;
            $err_data['res'] = $result;
            $err_data['arr_data'] = $err_data;
            output_error("获取信息错误");
        }
    }

    /**
     * 小程序绑定手机（阿闻爱省钱）
     */
    public function bind_mobileasqOp()
    {
        $model_mb_payment = Model('mb_payment');
        $condition = array();
        $condition['payment_code'] = "wxpay_jsapi";
        $mb_payment_info = $model_mb_payment->getMbPaymentOpenInfo($condition);
        $payment_config = $mb_payment_info['payment_config'];
        $data = json_decode(file_get_contents('php://input'), true);
        $openid = $data['info']['openid'];
        $unionid = $data['info']['unionid'];
        $iv = $data['info']['iv'];
        $result = $this->decryptData($payment_config['miniappId3'], $data['info']['code_key'], $data['info']['encryptedData'], $iv, $data);
        $arrData = json_decode($result, true);
        $phone = $arrData['phoneNumber'];
        if ($phone) {
            $logic_connect_api = Logic('connect_api');
            $state_data = $logic_connect_api->smsRegisterBindMiniasq($phone, $openid, $unionid);
            $this->connect_output_data($state_data);
        } else {
            output_error("获取信息错误");
        }
    }

    /**
     * 小程序绑定手机（阿闻商城）
     */
    public function bind_mobilemallOp()
    {
        $model_mb_payment = Model('mb_payment');
        $condition = array();
        $condition['payment_code'] = "wxpay_jsapi";
        $mb_payment_info = $model_mb_payment->getMbPaymentOpenInfo($condition);
        $payment_config = $mb_payment_info['payment_config'];
        $data = json_decode(file_get_contents('php://input'), true);
        $openid = $data['info']['openid'];
        $iv = $data['info']['iv'];
        $result = $this->decryptData($payment_config['miniappId4'], $data['info']['session_key'], $data['info']['encryptedData'], $iv, $data);
        $arrData = json_decode($result, true);
        $phone = $arrData['phoneNumber'];
        if ($phone) {
            $logic_connect_api = Logic('connect_api');
            $state_data = $logic_connect_api->smsRegisterBindMinimall($phone, $openid);
            $this->connect_output_data($state_data);
        } else {
            output_error("获取信息错误");
        }
    }

    /**
     * 获取微信昵称和头像
     */
    public function bind_userinfoasqOp()
    {
        $user_info['member_truename'] = trim($_POST['nickname']);
        $user_info['member_wxavatar'] = trim($_POST['avatar']);
        $member_id = $this->getMemberIdIfExists();
        if ($member_id) {
            /** @var connect_apiLogic $logic_connect_api */
            $logic_connect_api = Logic('connect_api');
            $state_data = $logic_connect_api->editWeixinUserInfo($member_id, $user_info);
            $this->connect_output_data($state_data);
        } else {
            output_error("获取信息错误");
        }
    }

    public function decryptData($miniappid, $sessionKey, $encryptedData, $iv, &$data)
    {
        #$this->sessionKey = "9F7p8vTcsxeGrLGXDr9bUw==";
        #$this->sessionKey = "wAiA1OxPRK+jUWEkOfmpJg==";
        #$miniappid = "wx8aec93186319c883";
        if (strlen($sessionKey) != 24) {
            return -41001;
        }
        $aesKey = base64_decode($sessionKey);
        if (strlen($iv) != 24) {
            return -41002;
        }
        $aesIV = base64_decode($iv);
        $aesCipher = base64_decode($encryptedData);
        $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);
        $dataObj = json_decode($result);
        if ($dataObj == NULL) {
            return -41003;
        }
        if ($dataObj->watermark->appid != $miniappid) {
            return -41003;
        }
        $data = $result;
        return $data;
    }

    /**
     * 
     * 通过token得到用户登录key(阿闻宠物)
     */
    public function getAwenLoginInfoNewOp()
    {
        $http_token = trim($_SERVER['HTTP_TOKEN']);
        if (!$http_token) {
            output_error('缺少必填信息');
        }

        try {
            action(new VerifyJwtSignAction($http_token));
        } catch (Exception $e) {
            output_error($e->getMessage());
        }

        $exp = explode('.', $http_token);
        $login_info = json_decode(base64_decode($exp[1]), true);
        if (!is_array($login_info)) {
            output_error('异常请求信息');
        }
        $phone = $login_info['mobile'];
        $openid = $login_info['openid']; // 新增
        $scrm_user_id = $login_info['scrmid']; // 新增

        // 微信校验openid，platform_id 不存在或者1：微信，2：支付宝
        if (empty($openid) && (empty($login_info['platform_id']) || $login_info['platform_id'] == 1)) {
            wkcache("aw_login_tips_" . $scrm_user_id, $http_token, 600);
            output_error('微信信息异常');
        }
        if (!preg_match('/^0?(13|15|17|18|14|19|16)[0-9]{9}$/i', $phone)) {
            output_error('手机号码格式错误');
        }
        if (empty($scrm_user_id)) {
            output_error('scrm信息异常');
        }

        /** @var connect_apiLogic $logic_connect_api */
        $logic_connect_api = Logic('connect_api');
        $state_data = $logic_connect_api->awenpetBindUser($login_info);
        $login_info['member_id'] = intval($state_data['member_id']);
        $state_data['key'] = $logic_connect_api->getJwtToken($login_info);
        $this->connect_output_data($state_data);
    }

    /**
     * Notes:APP手机用户验证
     * User: rocky
     * DateTime: 2021/8/4 15:30
     */
    public function getAwenLoginInfoAppOp()
    {
        $user_data = file_get_contents('php://input');
        $user_info = json_decode($user_data, true);
        $sign = $user_info['sign'];
        unset($user_info['sign']);
        $sure_sign = getSignature($user_info);
        if (!empty($user_info)) {
            if ($sure_sign == $sign) {
                /** @var connect_apiLogic $logic_connect_api */
                $logic_connect_api = Logic('connect_api');
                $res = $logic_connect_api->addMemeber($user_info);
                if ($res['state']) {
                    output_data(1);
                } else {
                    output_error($res['msg']);
                }
            } else {
                output_error("签名错误");
            }
        } else {
            output_error("解析数据失败");
        }
    }

    /**
     * Notes:通过手机号获取token
     * User: rocky
     * DateTime: 2021/10/25 12:30
     */
    public function getTokenOp()
    {
        // 对比授权key，如果是线上，还要核对ip
        if ($_REQUEST['api_key'] <> self::KEY) {
            output_error("签名错误");
        }
        if (empty($_POST['mobile'])) {
            output_error("参数错误");
        }
        $member = Model('member')->getMemberInfo(['member_mobile' => $_POST['mobile']]);
        $token = Logic('connect_api')->getJwtToken([
            'scrmid' => $member['scrm_user_id'],
            'openid' => $member['weixin_mini_openidasq'],
            'phone' => $member['member_mobile'],
            'mobile' => $member['member_mobile'],
            'member_id' => intval($member['member_id']),
        ]);
        output_data($token);
    }
}
