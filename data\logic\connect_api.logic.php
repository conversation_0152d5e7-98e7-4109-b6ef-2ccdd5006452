<?php

/**
 * 第三方账号登录和注册行为
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Upet\Models\DcMemberCardRelation;
use Upet\Modules\Member\Queues\UpdateMemberVipQueue;
use Upet\Models\Eshop\Users;
use Upet\Models\Eshop\PetInvite;

defined('InShopNC') or exit('Access Invalid!');

class connect_apiLogic
{

    /**
     * 登录开关状态
     * @return array
     */
    public function getStateInfo()
    {
        $state_array = array();
        $state_array['pc_qq'] = C('qq_isuse'); //PC网页端的QQ互联
        $state_array['pc_sn'] = C('sina_isuse'); //PC网页端的新浪微博
        $state_array['connect_qq'] = C('app_qq_isuse'); //手机客户端的QQ互联
        $state_array['connect_sn'] = C('app_sina_isuse'); //手机客户端的新浪微博
        $state_array['connect_wx'] = C('app_weixin_isuse'); //手机客户端的微信登录
        $state_array['connect_wap_wx'] = C('wap_weixin_isuse'); //wap的微信登录
        $state_array['connect_sms_reg'] = C('sms_register'); //手机注册
        $state_array['connect_sms_lgn'] = C('sms_login'); //手机登录
        $state_array['connect_sms_psd'] = C('sms_password'); //手机短信找回密码
        return $state_array;
    }

    /**
     * 获取wap的微信登录
     * @return string
     */
    public function getWxOAuth2Url()
    {
        $weixin_appid = C('wap_weixin_appid');

        $api_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $weixin_appid .
            '&redirect_uri=' . urlencode(MOBILE_SITE_URL . '/index.php?act=connect&op=index') .
            '&response_type=code&scope=snsapi_userinfo&state=' . base64_encode($_GET['ref_url']) . '#wechat_redirect';
        return $api_url;
    }

    /**
     * 发送手机动态码
     * @param string $phone
     * @param string $log_type
     * @return array
     */
    public function sendCaptcha($phone, $log_type, $chkmember = true)
    {
        $model_sms_log = Model('sms_log');
        $state = true;
        $msg = '手机动态码发送成功';
        $sms_log = $this->ipCaptcha($log_type);
        if (!empty($sms_log) && ($sms_log['add_time'] > TIMESTAMP - DEFAULT_CONNECT_SMS_TIME)) { //同一IP[n]秒内只能发一条短信
            $state = false;
            $msg = '同一IP地址' . DEFAULT_CONNECT_SMS_TIME . '秒内，请勿多次获取动态码！';
        }
        $condition = array();
        $condition['log_phone'] = $phone;
        $condition['log_type'] = $log_type;
        $sms_log = $model_sms_log->getSmsInfo($condition);
        if ($state && !empty($sms_log) && ($sms_log['add_time'] > TIMESTAMP - DEFAULT_CONNECT_SMS_TIME)) { //同一手机号IP[n]秒内只能发一条短信
            $state = false;
            $msg = '同一手机号' . DEFAULT_CONNECT_SMS_TIME . '秒内，请勿多次获取动态码！';
        }
        //如果门店登录注册短信可以减少限制 默认为24小时受限制
        $time24 = TIMESTAMP - 60 * 60 * 24;
        if ($_SESSION['chain_login'] == 1) {
            $time24 = TIMESTAMP - 60;
        }
        $condition = array();
        $condition['log_phone'] = $phone;
        $condition['add_time'] = array('egt', $time24);
        $num = $model_sms_log->getSmsCount($condition);
        if ($state && $num >= DEFAULT_CONNECT_SMS_PHONE) { //同一手机号24小时内只能发5条短信
            $state = false;
            $msg = '同一手机号24小时内，请勿多次获取动态码！';
        }
        $condition = array();
        $condition['log_ip'] = getIp();
        $condition['add_time'] = array('egt', $time24);
        $num = $model_sms_log->getSmsCount($condition);
        if ($state && $num >= DEFAULT_CONNECT_SMS_IP) { //同一IP24小时内只能发20条短信
            $state = false;
            $msg = '同一IP24小时内，请勿多次获取动态码！';
        }
        if ($state == true) {
            $log_array = array();
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            $captcha = rand(100000, 999999);
            $log_msg = '【' . C('site_name') . '】您于' . date("Y-m-d");
            switch ($log_type) {
                case '1':
                    if (C('sms_register') != 1) {
                        $state = false;
                        $msg = '系统没有开启手机注册功能';
                    }
                    if (!empty($member) && $chkmember) { //检查手机号是否已被注册
                        $state = false;
                        $msg = '当前手机号已被注册，请更换其他号码。';
                    }
                    $log_msg .= '申请注册会员，短信验证码：' . $captcha . '。';
                    break;
                case '2':
                    if (C('sms_login') != 1) {
                        $state = false;
                        $msg = '系统没有开启手机登录功能';
                    }
                    if (empty($member)) { //检查手机号是否已绑定会员
                        $state = false;
                        $msg = '当前手机号未注册，请检查号码是否正确。';
                    }
                    $log_msg .= '申请登录，短信验证码：' . $captcha . '。';
                    $log_array['member_id'] = $member['member_id'];
                    $log_array['member_name'] = $member['member_name'];
                    break;
                case '3':
                    if (C('sms_password') != 1) {
                        $state = false;
                        $msg = '系统没有开启手机找回密码功能';
                    }
                    if (empty($member)) { //检查手机号是否已绑定会员
                        $state = false;
                        $msg = '当前手机号未注册，请检查号码是否正确。';
                    }
                    $log_msg .= '申请重置登录密码，短信验证码：' . $captcha . '。';
                    $log_array['member_id'] = $member['member_id'];
                    $log_array['member_name'] = $member['member_name'];
                    break;
                case '4':
                    if (C('sms_register') != 1) {
                        $state = false;
                        $msg = '系统没有开启手机注册功能';
                    }
                    if (C('sms_login') != 1) {
                        $state = false;
                        $msg = '系统没有开启手机登录功能';
                    }
                    if (!empty($member) && $chkmember) { //检查手机号是否已被注册
                        /*$state = false;
                        $msg = '当前手机号已被注册，请更换其他号码。';*/
                        $log_array['member_id'] = $member['member_id'];
                        $log_array['member_name'] = $member['member_name'];
                        $log_msg .= '申请登录，短信验证码：' . $captcha . '。';
                    }
                    if (empty($member)) { //检查手机号是否已绑定会员
                        //$msg = '当前手机号未注册，请检查号码是否正确。';
                        $log_msg .= '申请注册会员，短信验证码：' . $captcha . '。';
                    }
                    break;
                default:
                    $state = false;
                    $msg = '参数错误';
                    break;
            }
            if ($state == true) {
                $this->_sendAdmin();
                $sms = new Sms();
                $result = $sms->send($phone, $log_msg);
                if ($result) {
                    $log_array['log_phone'] = $phone;
                    $log_array['log_captcha'] = $captcha;
                    $log_array['log_ip'] = getIp();
                    $log_array['log_msg'] = $log_msg;
                    $log_array['log_type'] = $log_type;
                    $log_array['add_time'] = TIMESTAMP;
                    $model_sms_log->addSms($log_array);
                } else {
                    $state = false;
                    $msg = '手机短信发送失败';
                }
            }
        }
        $state_data = array(
            'state' => $state,
            'sms_time' => DEFAULT_CONNECT_SMS_TIME,
            'msg' => $msg
        );
        setNcCookie('seccode', '', -3600); //发送清空客户端
        return $state_data;
    }

    /**
     * 通知管理员
     */
    private function _sendAdmin()
    {
        $sms = new Sms();
        //start统计超量通知管理员
        $rkey = "sendCounts" . date("Y-m-d", time());
        $arr = rkcache($rkey);
        $i = $arr['snedNum'] + 1;

        $max = 3000; //阈值
        $errmax = 8000; //最大值

        if ($i >= $max) {
            if (!$arr['sendAdmin']) {
                $sms->send("15986814085", "【阿闻商城】短信今天发送量已经超{$max}条，请留意是否被攻击"); //袁国波
                $result = $sms->send("18319034357", "【阿闻商城】短信今天发送量已经超{$max}条，请留意是否被攻击"); //余彬
                $param = $arr;
                if ($result) {
                    $param['sendAdmin'] = true;
                }
                $param['snedNum'] = $i;
                wkcache($rkey, $param, 3600 * 24 * 30); //记录30天
            }
            if ($i >= $errmax) {
                die("请留意是否被攻击");
            }
        } else {
            $param = array();
            $param['sendAdmin'] = false; //是否通知
            $param['snedNum'] = $i;
            wkcache($rkey, $param, 3600 * 24 * 30); //记录30天
        }
        //end
    }

    /**
     * 手机注册
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @return array
     */
    public function smsRegister($phone, $captcha, $password, $client)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, 1); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //检查手机号是否已被注册
                $state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';
                return $state_data;
            }
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet', $num);
            $member = array();
            $member['member_name'] = $member_name;
            $member['member_passwd'] = $password;
            /* $member['member_email'] = ''; */
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $result = $model_member->addMember($member);
            if ($result) {
                $member = $model_member->getMemberInfo(array('member_name' => $member_name));

                //如果查询失效，直接获取数据
                if (!$member) {
                    $member = array();
                    $member['member_name'] = $member_name;
                    $member['member_passwd'] = $password;
                    /* $member['member_email'] = ''; */
                    $member['member_mobile'] = $phone;
                    $member['member_mobile_bind'] = 1;
                    $member['member_id'] = $result;
                }

                $member_id = $member['member_id'];
                $token = $this->getUserToken($member, $client);

                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $member_id;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        return $state_data;
    }

    /**
     * 小程序 手机号码 注册，登录(阿闻宠物-北京团队用)
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @param string $openid
     * @return array
     */
    public function smsMobile($phone, $captcha, $password, $client, $openid = "", $type = 1)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, $type); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //登录
                $member_id = $member['member_id'];
                $token = $this->getUserToken($member, $client, $openid, 2);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '登录失败';
                }
                /*$state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';
                return $state_data;*/
            } else { //注册
                $num = substr($phone, -4);
                $member_name = $this->getMemberName('upet', $num);
                $member = array();
                $member['member_name'] = $member_name;
                $member['member_passwd'] = $password ? $password : md5($phone);
                /* $member['member_email'] = ''; */
                $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1;
                $member['weixin_mini_openid'] = $openid;
                $member['weixin_mini_addtime'] = TIMESTAMP;
                $member['geval_comment_status'] = 3; //注册来源为小程序
                $result = $model_member->addMember($member);
                if ($result) {
                    $member = $model_member->getMemberInfo(array('member_id' => $result));
                    //如果查询失效，直接获取数据
                    if (!$member) {
                        $member = array();
                        $member['member_name'] = $member_name;
                        $member['member_passwd'] = $password;
                        /* $member['member_email'] = ''; */
                        $member['member_mobile'] = $phone;
                        $member['member_mobile_bind'] = 1;
                        $member['member_id'] = $result;
                    }
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client, $openid, 2);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member_name;
                        $state_data['userid'] = $member_id;
                    } else {
                        $state_data['msg'] = '会员登录失败';
                    }
                } else {
                    $state_data['msg'] = '会员信息保存错误';
                }
            }
        }
        return $state_data;
    }

    /**
     * 小程序 手机号码 注册，登录(阿闻智慧宠物医院-自用)
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @param string $openid
     * @return array
     */
    public function smsMobileShop($phone, $captcha, $password, $client, $openid = "", $type = 1)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, $type); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //登录
                $member_id = $member['member_id'];
                $token = $this->getUserToken($member, $client, $openid, 1);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '登录失败';
                }
                /*$state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';
                return $state_data;*/
            } else { //注册
                $num = substr($phone, -4);
                $member_name = $this->getMemberName('upet', $num);
                $member = array();
                $member['member_name'] = $member_name;
                $member['member_passwd'] = $password ? $password : md5($phone);
                /* $member['member_email'] = ''; */
                $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1;
                $member['weixin_mini_openidshop'] = $openid;
                $member['weixin_mini_addtimeshop'] = TIMESTAMP;
                $member['geval_comment_status'] = 3; //注册来源为小程序 阿闻智慧宠物医院
                $result = $model_member->addMember($member);
                if ($result) {
                    $member = $model_member->getMemberInfo(array('member_id' => $result));
                    //如果查询失效，直接获取数据
                    if (!$member) {
                        $member = array();
                        $member['member_name'] = $member_name;
                        $member['member_passwd'] = $password;
                        /* $member['member_email'] = ''; */
                        $member['member_mobile'] = $phone;
                        $member['member_mobile_bind'] = 1;
                        $member['member_id'] = $result;
                    }
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client, $openid, 1);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member_name;
                        $state_data['userid'] = $member_id;
                    } else {
                        $state_data['msg'] = '会员登录失败';
                    }
                } else {
                    $state_data['msg'] = '会员信息保存错误';
                }
            }
        }
        return $state_data;
    }

    /**
     * 小程序 手机号码 注册，登录(阿闻商城-自用)
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @param string $openid
     * @return array
     */
    public function smsMobileMall($phone, $captcha, $password, $client, $openid = "", $type = 1)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, $type); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //登录
                $member_id = $member['member_id'];
                $token = $this->getUserToken($member, $client, $openid, 4);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '登录失败';
                }
                /*$state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';
                return $state_data;*/
            } else { //注册
                $num = substr($phone, -4);
                $member_name = $this->getMemberName('upet', $num);
                $member = array();
                $member['member_name'] = $member_name;
                $member['member_passwd'] = $password ? $password : md5($phone);
                /* $member['member_email'] = ''; */
                $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1;
                $member['weixin_mini_openidmall'] = $openid;
                $member['weixin_mini_addtimemall'] = TIMESTAMP;
                $member['geval_comment_status'] = 6; //注册来源为小程序阿闻商城
                $result = $model_member->addMember($member);
                if ($result) {
                    $member = $model_member->getMemberInfo(array('member_id' => $result));
                    //如果查询失效，直接获取数据
                    if (!$member) {
                        $member = array();
                        $member['member_name'] = $member_name;
                        $member['member_passwd'] = $password;
                        /* $member['member_email'] = ''; */
                        $member['member_mobile'] = $phone;
                        $member['member_mobile_bind'] = 1;
                        $member['member_id'] = $result;
                    }
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member_name;
                        $state_data['userid'] = $member_id;
                    } else {
                        $state_data['msg'] = '会员登录失败';
                    }
                } else {
                    $state_data['msg'] = '会员信息保存错误';
                }
            }
        }
        return $state_data;
    }

    /**
     * 小程序 手机号码 注册，登录(阿闻爱省钱-自用)
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @param string $openid
     * @return array
     */
    public function smsMobileAsq($phone, $captcha, $password, $client, $openid = "", $type = 1)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, $type); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //登录
                $member_id = $member['member_id'];
                $token = $this->getUserToken($member, $client, $openid, 3);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '登录失败';
                }
                /*$state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';
                return $state_data;*/
            } else { //注册
                $num = substr($phone, -4);
                $member_name = $this->getMemberName('upet', $num);
                $member = array();
                $member['member_name'] = $member_name;
                $member['member_passwd'] = $password ? $password : md5($phone);
                /* $member['member_email'] = ''; */
                $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1;
                $member['weixin_mini_openidasq'] = $openid;
                $member['weixin_mini_addtimeasq'] = TIMESTAMP;
                $member['geval_comment_status'] = 5; //注册来源为小程序阿闻爱省钱
                $result = $model_member->addMember($member);
                if ($result) {
                    $member = $model_member->getMemberInfo(array('member_id' => $result));
                    //如果查询失效，直接获取数据
                    if (!$member) {
                        $member = array();
                        $member['member_name'] = $member_name;
                        $member['member_passwd'] = $password;
                        /* $member['member_email'] = ''; */
                        $member['member_mobile'] = $phone;
                        $member['member_mobile_bind'] = 1;
                        $member['member_id'] = $result;
                    }
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client, $openid, 3);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member_name;
                        $state_data['userid'] = $member_id;
                    } else {
                        $state_data['msg'] = '会员登录失败';
                    }
                } else {
                    $state_data['msg'] = '会员信息保存错误';
                }
            }
        }
        return $state_data;
    }

    /**
     * 绑定手机注册
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @return array
     */
    public function smsRegisterBind($phone, $captcha, $client, $member_info)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, 1); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            if (empty($member_info)) {
                $state_data['state'] = false;
                $state_data['msg'] = '登录过期，请登录重新绑定';
                return $state_data;
            }
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
            if (!empty($member)) { //检查手机号是否已被注册
                $updateData = array();
                if ($member_info['member_qqopenid'] != "") {
                    $updateData['member_qqopenid'] = $member_info['member_qqopenid'];
                    $updateData['member_qqinfo'] = $member_info['member_qqinfo'];
                } elseif ($member_info['member_sinaopenid'] != "") {
                    $updateData['member_sinaopenid'] = $member_info['member_sinaopenid'];
                    $updateData['member_sinainfo'] = $member_info['member_sinainfo'];
                } elseif ($member_info['weixin_unionid'] != "") {
                    $updateData['weixin_unionid'] = $member_info['weixin_unionid'];
                    $updateData['weixin_info'] = $member_info['weixin_info'];
                }
                if (is_array($updateData) && !empty($updateData)) {
                    $result = $model_member->where(array("member_id" => $member['member_id']))->update($updateData);
                    if ($result) {
                        //$member = $model_member->getMemberInfo(array('member_name'=> $member_name));
                        //$model_member->createSession($member,false);//自动登录
                        $member_id = $member['member_id'];
                        $token = $this->getUserToken($member, $client);
                        if ($token) {
                            $state_data['key'] = $token;
                            $state_data['username'] = $member['member_name'];
                            $state_data['userid'] = $member_id;
                        } else {
                            $state_data['msg'] = '手机绑定失败';
                        }
                    } else {
                        $state_data['msg'] = '绑定失败';
                    }
                } else {
                    $state_data['msg'] = '绑定失败';
                }
                /*$state_data['state'] = false;
                $state_data['msg'] = '当前手机号已被注册，请更换其他号码。';*/
                return $state_data;
            } else {
                /* $num = substr($phone,-4);
                $member_name = $this->getMemberName('mb', $num); */
                $member = array();
                /* $member['member_name'] = $member_name; */
                /* $member['member_passwd'] = $password; */
                /* $member['member_email'] = ''; */
                $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1;
                $result = $model_member->where(array("member_id" => $member_info['member_id']))->update($member);;
                if ($result) {
                    $member = $model_member->getMemberInfo(array('member_name' => $member_info['member_name']));
                    $token = $this->getUserToken($member, $client);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member_info['member_name'];
                        $state_data['userid'] = $member_info['member_id'];
                    } else {
                        $state_data['msg'] = '手机绑定失败';
                    }
                } else {
                    $state_data['msg'] = '会员信息保存错误';
                }
            }
        }
        return $state_data;
    }

    public function smsRegisterBindMini($phone, $openid, $client = "wap")
    {
        /** @var memberModel $model_member */
        $model_member = Model('member');
        $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
        if (!empty($member)) { //检查手机号是否已被注册
            $updateData = array();
            $updateData['weixin_mini_openid'] = $openid;
            $updateData['weixin_mini_addtime'] = time();
            if (is_array($updateData) && !empty($updateData)) {
                $result = $model_member->where(array("member_id" => $member['member_id']))->update($updateData);
                if ($result) {
                    if ($openid && $member['weixin_mini_openid'] <> $openid) {
                        $model_member->issuePayGiftVoucherWhenBindOpenid($member['member_id'], $member['member_name'], $openid);
                    }
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member['member_name'];
                        $state_data['userid'] = $member_id;
                        $state_data['state'] = true;
                    } else {
                        $state_data['msg'] = '手机绑定失败';
                    }
                } else {
                    $state_data['msg'] = '绑定失败';
                }
            } else {
                $state_data['msg'] = '绑定失败';
            }
        } else { //注册账号
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet', $num);
            $member = array();
            $member['member_name'] = $member_name;
            $member['weixin_mini_openid'] = $openid;
            $member['weixin_mini_addtime'] = time();
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $member['geval_comment_status'] = 3; //注册来源为小程序
            $result = $model_member->addMember($member);
            if ($result) {
                $member_info = $model_member->getMemberInfo(array('member_id' => $result));
                $member_id = $member_info['member_id'];
                $token = $this->getUserToken($member_info, $client);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        return $state_data;
    }

    /**
     * 阿闻智慧宠物医院小程序用户绑定(阿闻商城)
     * @param $phone    手机号
     * @param $openid   用户openid
     * @param string $client 来源
     * @return mixed
     */
    public function smsRegisterBindMinishop($phone, $openid, $client = "wap")
    {
        $model_member = Model('member');
        //redis 加锁操作
        $redis_logic = Logic('redis');
        $redis_get = $redis_logic->redisLock('awen_shop_' . $phone, $phone);
        if (!$redis_get) {
            return $state_data['msg'] = '正在登录中';
        }
        $field = "member_id,member_name,member_truename,member_wxavatar,weixin_mini_openidshop,member_login_num,member_login_time,member_login_ip";
        $member = $model_member->getMemberInfo(array('member_mobile' => $phone), $field);


        if (!empty($member)) { //检查手机号是否已被注册

            $updateData = array();
            $updateData['weixin_mini_openidshop'] = $openid;
            $updateData['weixin_mini_addtimeshop'] = time();
            $model_member->where(array("member_id" => $member['member_id']))->update($updateData);

            $member_id = $member['member_id'];
            $token = $this->getUserToken($member, $client);
            if ($token) {
                $state_data['key'] = $token;
                $state_data['username'] = $member['member_name'];
                $state_data['userid'] = $member_id;
                if ($member['member_truename'] && $member['member_wxavatar']) {

                    if (strstr($member['member_wxavatar'], 'http')) {
                        $member['member_avatar'] = $member['member_wxavatar'];
                    } else {
                        $member['member_avatar'] = str_replace('http:', 'https:', getMemberAvatar($member['member_wxavatar']));
                    }

                    $state_data['wxinfo'] = array('nickName' => $member['member_truename'], 'avatarUrl' => $member['member_wxavatar']);
                }
                $state_data['state'] = true;
            } else {
                $state_data['msg'] = '手机绑定失败';
            }
        } else { //注册账号
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet', $num);
            $member = array();
            $member['member_name'] = $member_name;
            $member['weixin_mini_openidshop'] = $openid;
            $member['weixin_mini_addtimeshop'] = time();
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $member['geval_comment_status'] = 3; //注册来源为小程序
            $result = $model_member->addMember($member);

            if ($result) {
                $member_info = $model_member->getMemberInfo(array('member_id' => $result), "member_id,member_name,member_login_num,member_login_time,member_login_ip");
                if (!$member_info) {
                    error_log(print_r($result, ture), 3, 'member_id.log');
                    error_log(print_r($member_info, ture), 3, 'member_info.log');
                }
                $member_id = $member_info['member_id'];
                $token = $this->getUserToken($member_info, $client);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        //登录成功删除key
        $redis_logic->delRedisLock('awen_shop_' . $phone);
        return $state_data;
    }

    /**
     * 阿闻爱省钱小程序用户绑定
     * @param $phone    手机号
     * @param $openid   用户openid
     * @param string $client 来源
     * @return mixed
     */
    public function smsRegisterBindMiniasq($phone, $openid, $unionid, $client = "wap")
    {
        $model_member = Model('member');
        //redis 加锁操作
        $state_data = array();
        $redis_logic = Logic('redis');
        $redis_get = $redis_logic->redisLock('awen_asq_' . $phone, $phone);
        if (!$redis_get) {
            return $state_data['msg'] = '正在登录中';
        }
        $field = "member_id,member_name,member_mobile,member_truename,member_wxavatar,weixin_mini_openidasq,member_login_num,member_login_time,member_login_ip,scrm_user_id";
        $member = $model_member->getMemberInfo(array('member_mobile' => $phone), $field);
        if (!empty($member)) {
            //检查手机号是否已被注册
            $updateData = array();
            $updateData['weixin_mini_openidasq'] = $openid;
            $updateData['weixin_unionid'] = $unionid;
            $updateData['weixin_mini_addtimeasq'] = time();
            if ($phone != $member['member_mobile']) {
                $updateData['member_mobile'] = $phone;
            }

            if (empty($member['scrm_user_id'])) {
                //新用户获取第三方scrmid
                $path = '/scrm-organization-api/user/addScrmNewUserPet';
                $data = [
                    'userName' => $member['member_name'],
                    'userMobile' => $phone
                ];
                $res = getAwasqContent($path, $data);

                if (empty($res['result']['userId'])) {
                    output_error('注册SCRM会员异常');
                }
                $member['scrm_user_id'] = $updateData['scrm_user_id'] = $res['result']['userId'];
            }

            $model_member->where(array("member_id" => $member['member_id']))->update($updateData);
            $member_id = $member['member_id'];
            $token = $this->getJwtToken(['scrmid' => $member['scrm_user_id'], 'openid' => $member['weixin_mini_openidasq'], 'phone' => $member['member_mobile']]);
            if ($token) {
                $state_data['key'] = $token;
                $state_data['username'] = $member['member_name'];
                $state_data['userid'] = $member_id;
                if ($member['member_truename'] && $member['member_wxavatar']) {
                    if (strstr($member['member_wxavatar'], 'http')) {
                        $member['member_avatar'] = $member['member_wxavatar'];
                    } else {
                        $member['member_avatar'] = str_replace('http:', 'https:', getMemberAvatar($member['member_wxavatar']));
                    }
                    $state_data['wxinfo'] = array('nickName' => $member['member_truename'], 'avatarUrl' => $member['member_avatar']);
                }
                $state_data['state'] = true;
            } else {
                $state_data['msg'] = '手机绑定失败';
            }
        } else { //注册账号
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet', $num);
            $member = array();
            $member['member_name'] = $member_name;
            $member['weixin_mini_openidasq'] = $openid;
            $member['weixin_unionid'] = $unionid;
            $member['weixin_mini_addtimeasq'] = time();
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $member['geval_comment_status'] = 5; //注册来源为阿闻爱省钱小程序

            //新用户获取第三方scrmid
            $path = '/scrm-organization-api/user/addScrmNewUserPet';
            $data = [
                'userName' => $member_name,
                'userMobile' => $phone
            ];
            $res = getAwasqContent($path, $data);
            $member['scrm_user_id'] = $res['result']['userId'];

            $result = $model_member->addMember($member);
            if ($result) {
                $token = $this->getJwtToken(['scrmid' => $member['scrm_user_id'], 'openid' => $member['weixin_mini_openidasq'], 'phone' => $member['member_mobile']]);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $result;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        //登录成功删除key
        $redis_logic->delRedisLock('awen_asq_' . $phone);
        return $state_data;
    }

    /**
     * 阿闻商城小程序用户绑定
     * @param $phone    手机号
     * @param $openid   用户openid
     * @param string $client 来源
     * @return mixed
     */
    public function smsRegisterBindMinimall($phone, $openid, $client = "wap")
    {
        $model_member = Model('member');
        $member = $model_member->getMemberInfo(array('member_mobile' => $phone));
        if (!empty($member)) { //检查手机号是否已被注册
            $updateData = array();
            $updateData['weixin_mini_openidmall'] = $openid;
            $updateData['weixin_mini_addtimemall'] = time();
            if (is_array($updateData) && !empty($updateData)) {
                $result = $model_member->where(array("member_id" => $member['member_id']))->update($updateData);
                if ($result) {
                    $member_id = $member['member_id'];
                    $token = $this->getUserToken($member, $client);
                    if ($token) {
                        $state_data['key'] = $token;
                        $state_data['username'] = $member['member_name'];
                        $state_data['userid'] = $member_id;
                        $state_data['state'] = true;
                    } else {
                        $state_data['msg'] = '手机绑定失败';
                    }
                } else {
                    $state_data['msg'] = '绑定失败';
                }
            } else {
                $state_data['msg'] = '绑定失败';
            }
        } else { //注册账号
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet', $num);
            $member = array();
            $member['member_name'] = $member_name;
            $member['weixin_mini_openidmall'] = $openid;
            $member['weixin_mini_addtimemall'] = time();
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $member['geval_comment_status'] = 6; //注册来源为阿闻小程序
            $result = $model_member->addMember($member);
            if ($result) {
                $member_info = $model_member->getMemberInfo(array('member_id' => $result));
                $member_id = $member_info['member_id'];
                $token = $this->getUserToken($member_info, $client);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $member_id;
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        return $state_data;
    }

    /**
     * 手机找回密码
     * @param string $phone
     * @param string $captcha
     * @param string $password
     * @param string $client
     * @return array
     */
    public function smsPassword($phone, $captcha, $password, $client)
    {
        $state_data = $this->checkSmsCaptcha($phone, $captcha, 3); //再次进行动态码验证
        $state = $state_data['state'];
        if ($state == true) {
            $model_member = Model('member');
            $member = $model_member->getMemberInfo(array('member_mobile' => $phone)); //检查手机号是否已被注册
            if (!empty($member)) {
                $new_password = md5($password);
                $model_member->editMember(array('member_id' => $member['member_id']), array('member_passwd' => $new_password));
                $member_id = $member['member_id'];
                $member_name = $member['member_name'];
                $token = $this->getUserToken($member, $client);
                if ($token) {
                    $state_data['key'] = $token;
                    $state_data['username'] = $member_name;
                    $state_data['userid'] = $member_id;
                } else {
                    $state_data['msg'] = '会员登录失败';
                }
            }
        }
        return $state_data;
    }

    /**
     * 验证动态码
     * @param string $phone
     * @param string $captcha
     * @param string $log_type
     * @return array
     */
    public function checkSmsCaptcha($phone, $captcha, $log_type)
    {
        $state = true;
        $msg = '手机动态码验证成功';
        $sms_log = $this->getLogCaptcha($phone, $captcha, $log_type);
        if (empty($sms_log) || ($sms_log['add_time'] < TIMESTAMP - 1800)) { //半小时内进行验证为有效
            $state = false;
            $msg = '动态码错误或已过期，重新输入';
        }
        $state_data = array(
            'state' => $state,
            'msg' => $msg
        );
        return $state_data;
    }

    /**
     * 查询手机动态码
     * @param string $phone
     * @param string $captcha
     * @param string $log_type
     * @return array
     */
    public function getLogCaptcha($phone, $captcha, $log_type)
    {
        $condition = array();
        $condition['log_phone'] = $phone;
        $condition['log_captcha'] = $captcha;
        $condition['log_type'] = intval($log_type);
        $model_sms_log = Model('sms_log');
        $sms_log = $model_sms_log->getSmsInfo($condition);
        return $sms_log;
    }

    /**
     * 获取移动端登录令牌
     * @param array $member
     * @param string $client
     * @param string $openid 微信用户openid
     * @param string $source 1.阿闻智慧门店 2.阿闻宠物(北京那边用) 3.阿闻爱省钱 4.阿闻商城
     * @return array
     */
    public function getUserToken($member, $client, $openid = "", $source = 1)
    {
        $model_mb_user_token = Model('mb_user_token');
        $mb_user_token_info = array();
        $token = md5($member['member_name'] . strval(TIMESTAMP) . strval(rand(0, 999999)));
        $mb_user_token_info['member_id'] = $member['member_id'];
        $mb_user_token_info['member_name'] = $member['member_name'];
        $mb_user_token_info['token'] = $token;
        $mb_user_token_info['login_time'] = TIMESTAMP;
        $mb_user_token_info['client_type'] = $client;
        $result = $model_mb_user_token->addMbUserToken($mb_user_token_info);
        if ($result) {
            $this->updateMemeberVip($member);
            return $token;
        } else {
            return 0;
        }
    }

    public function updateMemeberVip($member)
    {
        $model_member = Model('member');
        //添加会员积分
        $model_member->addPoint($member);
        //添加会员经验值
        $model_member->addExppoint($member);
        $update_info = array();
        $update_info['member_login_num'] = ($member['member_login_num'] + 1);
        $update_info['member_login_time'] = TIMESTAMP;
        $update_info['member_old_login_time'] = $member['member_login_time'];
        $update_info['member_login_ip'] = getIp();
        $update_info['member_old_login_ip'] = $member['member_login_ip'];
        $data_list = DcMemberCardRelation::getMemberInfo($member['scrm_user_id']);
        wkcache("scrm_user_" . $member['member_id'], $data_list, 7200);
        if (is_array($data_list) && !empty($data_list)) {
            foreach ($data_list as $value) {
                if ($value['cardtype'] == 1) { //卡类型(1-保障卡，2-会员卡)
                    $update_info['member_isbzk'] = 1;
                    $update_info['member_bzkstime'] = strtotime($value['expirystartdate']);
                    $update_info['member_bzketime'] = strtotime($value['expiryenddate']);
                } else {
                    $update_info['member_isvip'] = 1;
                    $update_info['member_vipstime'] = strtotime($value['expirystartdate']);
                    $update_info['member_vipetime'] = strtotime($value['expiryenddate']);
                }
            }
        }
        $model_member->editMember(array('member_id' => $member['member_id']), $update_info);
    }

    /**
     * 获得可用的会员名
     * @param string $prefix
     * @param int $num
     * @return string
     */
    public function getMemberName($prefix = 'user_', $num = 0)
    {
        $model_member = Model('member');
        do {
            $rand = substr(md5(uniqid(mt_rand(), true)), 0, 6);
            $member_name = $prefix . $rand;
            $member = $model_member->getMemberInfo(array('member_name' => $member_name), "member_id,member_name");
        } while ($member);
        return $member_name;
    }

    /**
     * 按IP查询手机动态码
     * @param string $log_type
     * @return array
     */
    public function ipCaptcha($log_type = '')
    {
        $condition = array();
        $condition['log_ip'] = getIp();
        $log_type = intval($log_type);
        if ($log_type > 0) {
            $condition['log_type'] = $log_type; //短信类型:1为注册,2为登录,3为找回密码
        }
        $model_sms_log = Model('sms_log');
        $sms_log = $model_sms_log->getSmsInfo($condition);
        return $sms_log;
    }

    /**
     * 微信注册
     * @param string $user_info
     * @param string $client
     * @return array
     */
    public function wxRegister($user_info, $client)
    {
        $state_data = array();
        $state_data['token'] = 0;
        $unionid = $user_info['unionid'];
        $nickname = $user_info['nickname'];
        if (!preg_match('/^[A-Za-z0-9\x{4e00}-\x{9fa5}_]/u', $nickname)) $nickname = 'wx';
        $rand = rand(100, 899);
        $member_name = $this->getMemberName($nickname, $rand);
        $password = rand(100000, 999999);
        $member = array();
        $member['member_name'] = $member_name;
        $member['member_passwd'] = $password;
        $member['member_email'] = '';
        $member['weixin_unionid'] = $unionid;
        $member['weixin_info'] = $user_info['weixin_info'];
        $model_member = Model('member');
        $result = $model_member->addMember($member);
        if ($result) {
            //拷贝头像导致有点慢，暂时屏蔽
            /* $headimgurl = $user_info['headimgurl'];//用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像）
            $avatar = @copy($headimgurl,BASE_UPLOAD_PATH.'/'.ATTACH_AVATAR."/avatar_$result.jpg");
            if($avatar) {
                $model_member->editMember(array('member_id'=> $result),array('member_avatar'=> "avatar_$result.jpg"));
            } */
            $member = $model_member->getMemberInfo(array('member_name' => $member_name));

            //如果查询失效，直接获取数据
            if (!$member) {
                $member = array();
                $member['member_name'] = $member_name;
                $member['member_passwd'] = $password;
                /* $member['member_email'] = ''; */
                /* $member['member_mobile'] = $phone;
                $member['member_mobile_bind'] = 1; */
                $member['member_id'] = $result;
            }
            if ($client == 'www') { //网站注册
                $member['password'] = $password;
                return $member;
            }
            $member_id = $member['member_id'];
            $token = $this->getUserToken($member, $client);
            if ($token) {
                $state_data['token'] = $token;
                $state_data['member']['member_name'] = $member_name;
                $state_data['member']['member_id'] = $member_id;
                $state_data['member']['member_passwd'] = $password;
            }
        }
        return $state_data;
    }

    /**
     * 获取微信用户个人信息
     * @param string $code
     * @param string $api_type
     * @return array
     */
    public function getWxUserInfo($code, $api_type = '')
    {
        $weixin_appid = C('weixin_appid');
        $weixin_secret = C('weixin_secret');
        if ($api_type == 'api') {
            $weixin_appid = C('app_weixin_appid');
            $weixin_secret = C('app_weixin_secret');
        }
        if ($api_type == 'wap') {
            $weixin_appid = C('wap_weixin_appid');
            $weixin_secret = C('wap_weixin_secret');
        }
        $url = 'https://api.weixin.qq.com/sns/oauth2/access_token?appid=' . $weixin_appid . '&secret=' . $weixin_secret .
            '&code=' . $code . '&grant_type=authorization_code';
        $access_token = $this->getUrlContents($url); //通过code获取access_token
        $code_info = json_decode($access_token, true);
        $user_info = array();
        if (!empty($code_info['access_token'])) {
            $token = $code_info['access_token'];
            $openid = $code_info['openid'];
            $url = 'https://api.weixin.qq.com/sns/userinfo?access_token=' . $token . '&openid=' . $openid;
            $result = $this->getUrlContents($url); //获取用户个人信息
            $user_info = json_decode($result, true);
            if (empty($user_info['unionid'])) {
                $user_info['unionid'] = $user_info['openid'];
            }
            $weixin_info = array();
            $weixin_info['unionid'] = $user_info['unionid'];
            $weixin_info['nickname'] = $user_info['nickname'];
            $weixin_info['openid'] = $user_info['openid'];
            $user_info['weixin_info'] = serialize($weixin_info);
        }
        return $user_info;
    }

    /**
     * 获取微信用户个人信息
     * @param string $code
     * @param string $api_type
     * @return array
     */
    public function getWxUserInfoUmeng($access_token, $openid)
    {
        $url = 'https://api.weixin.qq.com/sns/userinfo?access_token=' . $access_token . '&openid=' . $openid;
        $result = $this->getUrlContents($url); //获取用户个人信息
        $user_info = json_decode($result, true);
        if (empty($user_info['unionid'])) {
            $user_info['unionid'] = $user_info['openid'];
        }
        $weixin_info = array();
        $weixin_info['unionid'] = $user_info['unionid'];
        $weixin_info['nickname'] = $user_info['nickname'];
        $weixin_info['openid'] = $user_info['openid'];
        $user_info['weixin_info'] = serialize($weixin_info);
        return $user_info;
    }


    /**
     * 获取新浪OAuth2的authorize接口
     * @param string $sina_type
     * @return array
     */
    public function getSinaOAuth2Url($sina_type = '')
    {
        $client_id = C('sina_wb_akey');
        $client_secret = C('sina_wb_skey');

        require_once(BASE_RESOURCE_PATH . DS . 'api' . DS . 'sina' . DS . 'saetv2.ex.class.php');
        $oauth2 = new SaeTOAuthV2($client_id, $client_secret);

        $api_url = RESOURCE_SITE_URL . '/api/sina/return_url.php';
        $sina_url = '';
        if ($sina_type == 'api') { //WAP接口调用
            $sina_url = $oauth2->getAuthorizeURL($api_url, 'code', 'api' . base64_encode($_GET['ref_url']), 'mobile');
        } else {
            $sina_url = $oauth2->getAuthorizeURL($api_url, 'code', base64_encode($_GET['ref_url']));
        }
        return $sina_url;
    }

    /**
     * 获取新浪微博用户个人信息
     * @param string $code
     * @return array
     */
    public function getSinaUserInfo($code, $client = '', $token = array())
    {
        $client_id = C('sina_wb_akey');
        $client_secret = C('sina_wb_skey');
        if ($client == 'ios' || $client == 'android') {
            $client_id = C('app_sina_akey');
            $client_secret = C('app_sina_skey');
        }

        require_once(BASE_RESOURCE_PATH . DS . 'api' . DS . 'sina' . DS . 'saetv2.ex.class.php');
        $oauth2 = new SaeTOAuthV2($client_id, $client_secret);

        if (!empty($code)) {
            $api_url = RESOURCE_SITE_URL . '/api/sina/return_url.php';
            $params = array();
            $params['code'] = $code;
            $params['redirect_uri'] = $api_url;
            $token = $oauth2->getAccessToken('code', $params);
        }

        $oauth2->setToken($token['access_token']);
        $info = $oauth2->getUserById($token['uid']); //根据ID获取用户等基本信息
        return $info;
    }

    /**
     * 新浪注册
     * @param string $user_info
     * @param string $client
     * @return array
     */
    public function sinaRegister($user_info, $client)
    {
        $state_data = array();
        $state_data['token'] = 0;
        $openid = $user_info['id'];
        $nickname = $user_info['screen_name'];
        $sina_str = serialize($user_info);
        $rand = rand(100, 899);
        $member_name = $this->getMemberName($nickname, $rand);
        $password = rand(100000, 999999);
        $member = array();
        $member['member_name'] = $member_name;
        $member['member_passwd'] = $password;
        $member['member_email'] = '';
        $member['member_sinaopenid'] = $openid;
        $member['member_sinainfo'] = $sina_str;
        $model_member = Model('member');
        $result = $model_member->addMember($member);
        if ($result) {
            //拷贝头像导致有点慢，暂时屏蔽
            /* $avatar = @copy($user_info['avatar_large'],BASE_UPLOAD_PATH.'/'.ATTACH_AVATAR."/avatar_$result.jpg");
            if($avatar) {
                $model_member->editMember(array('member_id'=> $result),array('member_avatar'=> "avatar_$result.jpg"));
            } */
            $member = $model_member->getMemberInfo(array('member_name' => $member_name));
            if ($client == 'www') { //网站注册
                $member['password'] = $password;
                return $member;
            }
            $member_id = $member['member_id'];
            $token = $this->getUserToken($member, $client); //手机接口登录
            if ($token) {
                $state_data['token'] = $token;
                $state_data['member']['member_name'] = $member_name;
                $state_data['member']['member_id'] = $member_id;
                $state_data['member']['member_passwd'] = $password;
            }
        }
        return $state_data;
    }

    /**
     * 获取qq接口
     * @param string $qq_type
     * @return array
     */
    public function getQqOAuth2Url($qq_type = '')
    {
        $client_id = C('qq_appid');
        $client_secret = C('qq_appkey');

        require_once(BASE_RESOURCE_PATH . DS . 'api' . DS . 'qq' . DS . 'oauth.qq.class.php');
        $oauth2 = new OauthQq($client_id, $client_secret);

        $api_url = RESOURCE_SITE_URL . '/api/qq/return_url.php';
        $qq_url = '';
        if ($qq_type == 'api') { //WAP接口调用
            $qq_url = $oauth2->qq_login($api_url, 'api' . base64_encode($_GET['ref_url']), 'mobile');
        } else {
            $qq_url = $oauth2->qq_login($api_url, base64_encode($_GET['ref_url']));
        }
        return $qq_url;
    }

    /**
     * 获取qq用户个人信息
     * @param string $code
     * @return array
     */
    public function getQqUserInfo($code, $client = '', $token = '')
    {
        $client_id = C('qq_appid');
        $client_secret = C('qq_appkey');
        if ($client == 'ios' || $client == 'android') {
            $client_id = C('app_qq_akey');
            $client_secret = C('app_qq_skey');
        }

        require_once(BASE_RESOURCE_PATH . DS . 'api' . DS . 'qq' . DS . 'oauth.qq.class.php');
        $oauth2 = new OauthQq($client_id, $client_secret);
        $token = '';
        if (!empty($code)) {
            $api_url = RESOURCE_SITE_URL . '/api/qq/return_url.php';
            $token = $oauth2->qq_callback($api_url, $code);
        }
        $get_unionid = 0;
        $user = $oauth2->get_openid($token, $get_unionid);
        $openid = $user["openid"];
        $unionid = $user["unionid"];
        $info = array();
        if ($client == 'ios' || $client == 'android') {
            $openid = $_GET['open_id'];
            $unionid = $_GET['union_id'];
            $info = $oauth2->get_user_info_simple($token, $openid); //根据ID获取用户等基本信息,此接口仅支持移动端应用调用
            if (empty($info['nickname'])) {
                $openid = $_GET['open_id'];
                $unionid = $_GET['union_id'];
                $info['openid'] = $openid;
                $info['nickname'] = 'qq';
                if (!empty($_GET['nickname'])) $info['nickname'] = $_GET['nickname'];
                if (!empty($_GET['avatar'])) $info['figureurl_qq_2'] = $_GET['avatar'];
            }
        } else {
            $info = $oauth2->get_user_info($token, $openid); //根据ID获取用户等基本信息
        }

        if (!empty($info['nickname'])) {
            if ($unionid) $openid = $unionid;
            $info['openid'] = $openid;
        }
        return $info;
    }

    /**
     * qq注册
     * @param string $user_info
     * @param string $client
     * @return array
     */
    public function qqRegister($user_info, $client)
    {
        $state_data = array();
        $state_data['token'] = 0;
        $openid = $user_info['openid'];
        $nickname = $user_info['nickname'];
        if (!preg_match('/^[A-Za-z0-9\x{4e00}-\x{9fa5}_]/u', $nickname)) $nickname = 'qq';
        $qq_str = serialize($user_info);
        $rand = rand(100, 899);
        $member_name = $this->getMemberName($nickname, $rand);
        $password = rand(100000, 999999);
        $member = array();
        $member['member_name'] = $member_name;
        $member['member_passwd'] = $password;
        $member['member_email'] = '';
        $member['member_qqopenid'] = $openid;
        $member['member_qqinfo'] = $qq_str;
        $model_member = Model('member');
        $result = $model_member->addMember($member);
        if ($result) {
            //拷贝头像导致有点慢，暂时屏蔽
            /* $avatar = @copy($user_info['figureurl_qq_2'],BASE_UPLOAD_PATH.'/'.ATTACH_AVATAR."/avatar_$result.jpg");
            if($avatar) {
                $model_member->editMember(array('member_id'=> $result),array('member_avatar'=> "avatar_$result.jpg"));
            } */
            $member = $model_member->getMemberInfo(array('member_name' => $member_name));
            if ($client == 'www') { //网站注册
                $member['password'] = $password;
                return $member;
            }
            $member_id = $member['member_id'];
            $token = $this->getUserToken($member, $client); //手机接口登录
            if ($token) {
                $state_data['token'] = $token;
                $state_data['member']['member_name'] = $member_name;
                $state_data['member']['member_id'] = $member_id;
                $state_data['member']['member_passwd'] = $password;
            }
        }
        return $state_data;
    }

    /**
     * OAuth2.0授权认证
     * @param string $url
     * @return string
     */
    public function getUrlContents($url)
    {
        if (ini_get("allow_url_fopen") == "1") {
            return file_get_contents($url);
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_URL, $url);
            $result = curl_exec($ch);
            curl_close($ch);
            return $result;
        }
    }

    /**
     * 通过手机号获取用户key
     * @param $phone  手机号码
     * @param $openid 用户openid
     * @param string $scrmUserId
     * @param string $client
     */
    public function awenpetBindUser($login_info)
    {
        $phone = $login_info['mobile'];
        $scrmUserId = $login_info['scrmid'];
        $openid = $login_info['openid'];
        $unionid = $login_info['unionid'];
        $state_data = array();
        $state_data['state'] = false; //test
        //redis 加锁操作
        $redis_logic = Logic('redis');
        $redis_lock_key = 'awen_' . $scrmUserId;
        $redis_get = $redis_logic->redisLock($redis_lock_key, $scrmUserId);
        if (!$redis_get) {
            $state_data['msg'] = '正在登录中';
            return $state_data;
        }

        /** @var memberModel $model_member */
        $model_member = Model('member');
        $member = $model_member->getMiniProgramLoginMember($scrmUserId, $phone);
        $source = intval($_POST['source']);
        if (!empty($member)) { //检查手机号是否已被注册
            $updateData = array();
            $mid = $member['member_id'];
            if ($openid) {
                if ($_REQUEST['store_id'] == 2) {
                    $updateData['weixin_mini_openid2'] = $openid;
                } else {
                    $updateData['weixin_mini_openid'] = $openid;
                }
            }
            if ($unionid) {
                $updateData['weixin_unionid'] = $unionid;
            }
            $updateData['scrm_user_id'] = $scrmUserId;
            $updateData['weixin_mini_addtime'] = time();
            if (is_array($updateData) && !empty($updateData)) {
                $result = $model_member->where(array("member_id" => $member['member_id']))->update($updateData);
                if ($result) {
                    if ($_REQUEST['store_id'] == 2) {
                        if ($openid && $member['weixin_mini_openid2'] <> $openid) {
                            $model_member->issuePayGiftVoucherWhenBindOpenid($member['member_id'], $member['member_name'], $openid);
                        }
                    } else {
                        if ($openid && $member['weixin_mini_openid'] <> $openid) {
                            $model_member->issuePayGiftVoucherWhenBindOpenid($member['member_id'], $member['member_name'], $openid);
                        }
                    }
                    $this->updateMemeberVip($member);
                    $member_id = $member['member_id'];
                    $state_data['username'] = $member['member_name'];
                    $state_data['userid'] = $member_id;
                    $state_data['is_dis'] = $member['distri_state'] == "2";
                    $state_data['state'] = true;
                } else {
                    $state_data['msg'] = '绑定失败';
                }
            } else {
                $state_data['msg'] = '绑定失败';
            }
        } else { //注册账号
            $num = substr($phone, -4);
            $member_name = $this->getMemberName('upet_aw' . mt_rand(1, 100), $num);
            $member = array();
            $member['scrm_user_id'] = $scrmUserId;
            $member['member_name'] = $member_name;
            if ($_REQUEST['store_id'] == 2) {
                $member['weixin_mini_openid2'] = $openid;
                // 判断贵族小程序的新老用户来源
                log_info('贵族小程序的新老用户来源', ['source' => $source, 'scrm_user_id' => $scrmUserId]);
            } else {
                $member['weixin_mini_openid'] = $openid;
            }
            $member['weixin_unionid'] = $unionid;
            $member['weixin_mini_addtime'] = time();
            $member['member_mobile'] = $phone;
            $member['member_mobile_bind'] = 1;
            $member['geval_comment_status'] = 4; //注册来源为阿闻宠物
            $mid = $result = $model_member->addMember($member);
            if ($result > 0) {
                $member_id = $result;
                $state_data['username'] = $member_name;
                $state_data['userid'] = $member_id;
                $state_data['is_dis'] = false;
                $state_data['state'] = true;
                // 考虑线下开卡情况，这里再更新一下会员信息
                UpdateMemberVipQueue::dispatch($result);
            } else {
                $state_data['msg'] = '会员信息保存错误';
            }
        }
        $state_data['member_id'] = $member_id;

        //登录成功删除key
        $redis_logic->delRedisLock($redis_lock_key);

        //添加登录用户扩展数据
        Users::addUserIfNotExist($mid, [
            'scrm_user_id' => $scrmUserId,
            'member_id' => $mid,
            'org_id' => $_REQUEST['store_id'] ?: 1,
            'weixin_unionid' => $unionid,
            'weixin_mini_openid' => $openid,
            'source' => $source,
        ]);

        // 更新有未注册的邀请用户数据
        if ($_REQUEST['store_id'] == 2) {
            $unregistered = PetInvite::getUnregisteredByOpenId($openid);

            if (!empty($unregistered)) {
                // 需要更新的字段
                $updateData = [
                    'invitee_nickname' => $member['member_name'],
                    'invitee_mobile' => $member['member_mobile'],
                    'invitee_en_mobile' => base64_encode(rc4($member['member_mobile'])),
                    'invitee_register_status' => 1,
                    'invitee_register_time' => date('Y-m-d H:i:s'),
                ];
                PetInvite::updateInviteeInfo($openid, $updateData);
            }
        }
        //登录成功删除key
        $redis_logic->delRedisLock($redis_lock_key);

        return $state_data;
    }

    /**
     * 获取微信头像信息
     * @param $member_id    会员id
     * @param $user_info    微信用户基本信息
     */
    public function editWeixinUserInfo($member_id, $user_info)
    {
        $state_data['state'] = false;
        $state_data['msg'] = '更新失败';
        $model_member = Model('member');
        $result = $model_member->where(array("member_id" => $member_id))->update($user_info);
        if ($result) {
            $state_data['state'] = true;
            $state_data['msg'] = '更新成功';
        }
        return $state_data;
    }

    /**
     * @desc 生成jwt签名
     * <AUTHOR>
     * @date 2021-5-14
     */
    public function getJwtToken($params)
    {
        $time = time();
        $params['exp'] = $time + 3600 * 24;
        $params['iat'] = $time - 10;
        $header = ["typ" => "JWT", "alg" => "RS256"];

        $privateKey = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

        $segments = array();
        $segments[] = $this->urlsafeB64Encode(json_encode($header));
        $segments[] = $this->urlsafeB64Encode(json_encode($params));
        $msg = implode('.', $segments);

        $rs = openssl_sign($msg, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        if (!$rs) {
            return false;
        }

        $segments[] = $this->urlsafeB64Encode($signature);

        return implode('.', $segments);
    }

    private function urlsafeB64Encode($input)
    {
        return str_replace('=', '', strtr(base64_encode($input), '+/', '-_'));
    }

    public function addMemeber($user_info)
    {
        $state_data = array();
        $state_data['state'] = false;
        $model_member = Model('member');
        $member_info = $model_member->getMiniProgramLoginMember($user_info['scrmid'], $user_info['mobile']);
        if ($member_info) {
            if ($user_info['scrmid'] && $member_info['scrm_user_id'] <> $user_info['scrmid']) {
                $model_member->where(array("member_id" => $member_info['member_id']))->update([
                    'scrm_user_id' => $user_info['scrmid']
                ]);
            }
            $state_data['state'] = true;
            return $state_data;
        }
        $num = substr($user_info['mobile'], -4);
        $member_name = $this->getMemberName('upet', $num);
        $member = array();
        $member['member_name'] = $member_name;
        $member['weixin_mini_addtimemall'] = time();
        $member['member_mobile'] = $user_info['mobile'];
        $member['scrm_user_id'] = $user_info['scrmid'];
        $member['member_mobile_bind'] = 1;
        $member['geval_comment_status'] = 9;
        $result = $model_member->addMember($member);
        if ($result > 0) {
            $state_data['state'] = true;
        } else {
            $state_data['state'] = false;
            $state_data['msg'] = '会员信息保存错误';
        }
        return $state_data;
    }
}
